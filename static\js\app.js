// Application State
let socket = null;
let pendingData = [];
let selectedRecords = new Set();
let currentTab = 'dashboard';
let currentEditRecord = null;
let previousStats = null;

// DOM Elements
const elements = {
    // Navigation
    navLinks: document.querySelectorAll('.nav-link'),
    sidebarToggle: document.getElementById('sidebar-toggle'),
    sidebar: document.querySelector('.sidebar'),

    // Upload elements
    uploadArea: document.getElementById('upload-area'),
    fileInput: document.getElementById('file-input'),
    fileInputFiles: document.getElementById('file-input-files'),
    selectFolderBtn: document.getElementById('select-folder-btn'),
    selectFilesBtn: document.getElementById('select-files-btn'),

    // Processing elements
    processingPanel: document.getElementById('processing-panel'),
    progressFill: document.getElementById('progress-fill'),
    progressText: document.getElementById('progress-text'),
    processedCount: document.getElementById('processed-count'),
    totalCount: document.getElementById('total-count'),
    currentFile: document.getElementById('current-file'),
    currentStatus: document.getElementById('current-status'),

    // Data table elements
    dataTable: document.getElementById('data-table'),
    dataTbody: document.getElementById('data-tbody'),
    selectAllCheckbox: document.getElementById('select-all'),
    bulkActions: document.getElementById('bulk-actions'),
    bulkApproveBtn: document.getElementById('bulk-approve-btn'),
    bulkRejectBtn: document.getElementById('bulk-reject-btn'),
    bulkDeleteBtn: document.getElementById('bulk-delete-btn'),
    deleteAllBtn: document.getElementById('delete-all-btn'),
    selectedCount: document.getElementById('selected-count'),

    // Filters
    typeFilter: document.getElementById('type-filter'),
    searchInput: document.getElementById('search-input'),

    // Header elements
    pageTitle: document.getElementById('page-title'),
    pageSubtitle: document.getElementById('page-subtitle'),
    refreshAll: document.getElementById('refresh-all'),
    quickExport: document.getElementById('quick-export'),

    // Statistics elements
    totalFiles: document.getElementById('total-files'),
    processingFiles: document.getElementById('processing-files'),
    completedFiles: document.getElementById('completed-files'),
    totalRecords: document.getElementById('total-records'),
    pendingRecords: document.getElementById('pending-records'),
    approvedRecords: document.getElementById('approved-records'),
    totalAmount: document.getElementById('total-amount'),
    pendingBadge: document.getElementById('pending-badge'),
    approvedBadge: document.getElementById('approved-badge'),

    // Form Modal elements
    formModal: document.getElementById('form-modal'),
    formModalClose: document.getElementById('form-modal-close'),
    formModalCancel: document.getElementById('form-modal-cancel'),
    formModalSave: document.getElementById('form-modal-save'),
    formModalReject: document.getElementById('form-modal-reject'),
    formEditForm: document.getElementById('form-edit-form'),
    formPreview: document.getElementById('form-preview'),

    // Email Modal elements
    emailModal: document.getElementById('email-modal'),
    emailModalClose: document.getElementById('email-modal-close'),
    emailModalCancel: document.getElementById('email-modal-cancel'),
    emailModalSave: document.getElementById('email-modal-save'),
    emailModalReject: document.getElementById('email-modal-reject'),
    emailEditForm: document.getElementById('email-edit-form'),
    emailPreview: document.getElementById('email-preview'),
    emailTypeIndicator: document.getElementById('email-type-indicator'),
    emailTypeBadge: document.getElementById('email-type-badge'),

    // Invoice Modal elements
    invoiceModal: document.getElementById('invoice-modal'),
    invoiceModalClose: document.getElementById('invoice-modal-close'),
    invoiceModalCancel: document.getElementById('invoice-modal-cancel'),
    invoiceModalSave: document.getElementById('invoice-modal-save'),
    invoiceModalReject: document.getElementById('invoice-modal-reject'),
    invoiceEditForm: document.getElementById('invoice-edit-form'),
    invoicePreview: document.getElementById('invoice-preview'),

    // Connection status
    connectionStatus: document.getElementById('connection-status'),
    statusDot: document.getElementById('status-dot'),
    statusText: document.getElementById('status-text'),
    lastUpdate: document.getElementById('last-update'),

    // Activity timeline
    activityTimeline: document.getElementById('activity-timeline'),

    // Toast container
    toastContainer: document.getElementById('toast-container')
};

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing application...');
    initializeWebSocket();
    setupEventListeners();
    setupNavigation();
    setupModals();
    loadPendingData();
    updateStatistics();
    updateActivityTimeline();
});

// Navigation System
function setupNavigation() {
    elements.navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const tab = link.getAttribute('data-tab');
            if (tab) {
                switchTab(tab);
            }
        });
    });

    // Quick action handlers
    document.querySelectorAll('.quick-action').forEach(action => {
        action.addEventListener('click', (e) => {
            const actionType = action.getAttribute('data-action');
            handleQuickAction(actionType);
        });
    });

    // Sidebar toggle
    if (elements.sidebarToggle) {
        elements.sidebarToggle.addEventListener('click', () => {
            elements.sidebar.classList.toggle('collapsed');
        });
    }
}

function switchTab(tabName) {
    // Update navigation
    elements.navLinks.forEach(link => {
        link.parentElement.classList.remove('active');
    });

    const activeLink = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeLink) {
        activeLink.parentElement.classList.add('active');
    }

    // Update content
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    const activeTab = document.getElementById(`${tabName}-tab`);
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // Update page title
    const titles = {
        dashboard: { title: 'Επισκόπηση Συστήματος', subtitle: 'Παρακολούθηση και διαχείριση εξαγωγής δεδομένων' },
        upload: { title: 'Φόρτωση Αρχείων', subtitle: 'Ανέβασμα και επεξεργασία νέων αρχείων' },
        queue: { title: 'Ουρά Επεξεργασίας', subtitle: 'Διαχείριση και έγκριση εξαχθέντων δεδομένων' },
        approved: { title: 'Εγκεκριμένα Δεδομένα', subtitle: 'Προβολή και εξαγωγή εγκεκριμένων εγγραφών' },
        analytics: { title: 'Αναλυτικά Στοιχεία', subtitle: 'Στατιστικά και αναφορές συστήματος' },
        exports: { title: 'Διαχείριση Εξαγωγών', subtitle: 'Ιστορικό και διαχείριση εξαγωγών δεδομένων' }
    };

    if (elements.pageTitle && titles[tabName]) {
        elements.pageTitle.textContent = titles[tabName].title;
        elements.pageSubtitle.textContent = titles[tabName].subtitle;
    }

    currentTab = tabName;

    // Load data for specific tabs
    if (tabName === 'queue') {
        loadPendingData();
    }
}

function handleQuickAction(actionType) {
    switch (actionType) {
        case 'upload':
            switchTab('upload');
            break;
        case 'approve-all':
            if (selectedRecords.size > 0) {
                bulkUpdateRecords('approved');
            } else {
                showToast('Δεν έχετε επιλέξει εγγραφές', 'warning');
            }
            break;
        case 'export':
            exportToExcel();
            break;
        case 'analytics':
            switchTab('analytics');
            break;
    }
}

// Modal Setup
function setupModals() {
    // Form Modal
    if (elements.formModalClose) elements.formModalClose.addEventListener('click', closeAllModals);
    if (elements.formModalCancel) elements.formModalCancel.addEventListener('click', closeAllModals);
    if (elements.formModalSave) elements.formModalSave.addEventListener('click', () => saveRecord('form', 'approved'));
    if (elements.formModalReject) elements.formModalReject.addEventListener('click', () => saveRecord('form', 'rejected'));

    // Email Modal
    if (elements.emailModalClose) elements.emailModalClose.addEventListener('click', closeAllModals);
    if (elements.emailModalCancel) elements.emailModalCancel.addEventListener('click', closeAllModals);
    if (elements.emailModalSave) elements.emailModalSave.addEventListener('click', () => saveRecord('email', 'approved'));
    if (elements.emailModalReject) elements.emailModalReject.addEventListener('click', () => saveRecord('email', 'rejected'));

    // Invoice Modal
    if (elements.invoiceModalClose) elements.invoiceModalClose.addEventListener('click', closeAllModals);
    if (elements.invoiceModalCancel) elements.invoiceModalCancel.addEventListener('click', closeAllModals);
    if (elements.invoiceModalSave) elements.invoiceModalSave.addEventListener('click', () => saveRecord('invoice', 'approved'));
    if (elements.invoiceModalReject) elements.invoiceModalReject.addEventListener('click', () => saveRecord('invoice', 'rejected'));

    // Close modal on outside click
    [elements.formModal, elements.emailModal, elements.invoiceModal].forEach(modal => {
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeAllModals();
                }
            });
        }
    });
}

// WebSocket Functions
function initializeWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;

    socket = new WebSocket(wsUrl);

    socket.onopen = function(event) {
        updateConnectionStatus('connected', 'Συνδεδεμένο');
        showToast('Η σύνδεση με τον διακομιστή επιτεύχθηκε', 'success');
    };

    socket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        } catch (e) {
            console.log('WebSocket message:', event.data);
        }
    };

    socket.onclose = function(event) {
        updateConnectionStatus('disconnected', 'Αποσυνδεδεμένο');
        showToast('Η σύνδεση με τον διακομιστή διακόπηκε', 'warning');

        setTimeout(() => {
            updateConnectionStatus('connecting', 'Επανασύνδεση...');
            initializeWebSocket();
        }, 3000);
    };

    socket.onerror = function(error) {
        updateConnectionStatus('disconnected', 'Σφάλμα σύνδεσης');
        showToast('Σφάλμα σύνδεσης με τον διακομιστή', 'error');
    };
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'upload_started':
            showProcessingPanel();
            updateProgress(0, data.message);
            updateProcessingStats(0, data.total_files);
            addActivityItem('Ξεκίνησε η φόρτωση αρχείων', 'info');
            break;

        case 'upload_progress':
        case 'processing_progress':
            updateProgress(data.progress, data.message);
            if (data.processed && data.total) {
                updateProcessingStats(data.processed, data.total);
            }
            break;

        case 'file_processed':
            showToast(`Επεξεργάστηκε: ${data.data.filename}`, 'success');
            addActivityItem(`Επεξεργάστηκε αρχείο: ${data.data.filename}`, 'success');
            loadPendingData();
            updateStatistics();
            break;

        case 'file_duplicate':
            showToast(`Διπλότυπο αρχείο: ${data.data.filename}`, 'warning');
            addActivityItem(`Παραλείφθηκε διπλότυπο: ${data.data.filename}`, 'warning');
            break;

        case 'processing_completed':
            updateProgress(100, data.message);
            showToast(data.message, 'success');
            addActivityItem('Ολοκληρώθηκε η επεξεργασία', 'success');

            setTimeout(() => {
                hideProcessingPanel();
                loadPendingData();
                updateStatistics();
            }, 2000);
            break;

        case 'record_updated':
            showToast(data.message, 'success');
            addActivityItem(data.message, 'success');
            loadPendingData();
            updateStatistics();
            break;

        case 'bulk_update':
            showToast(data.message, 'success');
            addActivityItem(`Μαζική ενημέρωση: ${data.count} εγγραφές`, 'success');
            loadPendingData();
            updateStatistics();
            break;

        case 'record_deleted':
            showToast(data.message, 'info');
            addActivityItem('Διαγραφή εγγραφής', 'info');
            loadPendingData();
            updateStatistics();
            break;

        case 'bulk_deleted':
            showToast(data.message, 'info');
            addActivityItem(`Μαζική διαγραφή: ${data.count} εγγραφές`, 'warning');
            loadPendingData();
            updateStatistics();
            break;

        case 'pending_cleared':
            showToast(data.message, 'info');
            addActivityItem('Καθαρίστηκε η ουρά εκκρεμών', 'warning');
            loadPendingData();
            updateStatistics();
            break;

        case 'export_completed':
            showToast(data.message, 'success');
            addActivityItem('Ολοκληρώθηκε η εξαγωγή', 'success');
            break;

        case 'error':
            showToast(data.message, 'error');
            addActivityItem('Σφάλμα συστήματος', 'error');
            hideProcessingPanel();
            break;
    }
}

// Enhanced Table Row Creation
function createTableRow(record) {
    const typeClass = `type-${record.type.toLowerCase()}`;
    const priorityClass = record.priority ? `priority-${record.priority.toLowerCase()}` : '';

    return `
        <tr data-id="${record.id}">
            <td class="checkbox-col">
                <label class="checkbox-container">
                    <input type="checkbox" class="record-checkbox" value="${record.id}">
                    <span class="checkmark"></span>
                </label>
            </td>
            <td>
                <span class="type-badge ${typeClass}">${translateType(record.type)}</span>
            </td>
            <td title="${record.source_file || ''}">${truncateText(record.source_file, 20)}</td>
            <td>${record.client_name || '-'}</td>
            <td>${record.company || '-'}</td>
            <td>${record.email || '-'}</td>
            <td>${record.phone || '-'}</td>
            <td>${record.service_interest || '-'}</td>
            <td>${formatAmount(record.total_amount || record.amount)}</td>
            <td>
                ${record.priority ? `<span class="${priorityClass}">${record.priority}</span>` : '-'}
            </td>
            <td>${formatDate(record.date_extracted)}</td>
            <td class="actions-col">
                <div class="actions">
                    <button class="btn-small btn-preview btn-edit" data-id="${record.id}" data-type="${record.type}" title="Επεξεργασία">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-success btn-approve" data-id="${record.id}" title="Έγκριση">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn-small btn-danger btn-reject" data-id="${record.id}" title="Απόρριψη">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="btn-small btn-secondary btn-delete" data-id="${record.id}" title="Διαγραφή">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// Enhanced Edit Modal Opening
function openEditModal(recordId) {
    const record = pendingData.find(r => r.id == recordId);
    if (!record) return;

    currentEditRecord = record;

    switch (record.type) {
        case 'FORM':
            openFormModal(record);
            break;
        case 'EMAIL':
            openEmailModal(record);
            break;
        case 'INVOICE':
            openInvoiceModal(record);
            break;
    }
}

// Form Modal Functions
function openFormModal(record) {
    const formElements = {
        'form-record-id': record.id,
        'form-client-name': record.client_name || '',
        'form-email': record.email || '',
        'form-phone': record.phone || '',
        'form-company': record.company || '',
        'form-service': record.service_interest || '',
        'form-priority': record.priority || '',
        'form-message': record.message || ''
    };

    Object.entries(formElements).forEach(([elementId, value]) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = value;
        } else {
            console.warn(`Form element not found: ${elementId}`);
        }
    });

    loadFormPreview(record.source_file);

    if (elements.formModal) {
        elements.formModal.classList.add('active');
    }
}

async function loadFormPreview(filename) {
    if (!elements.formPreview) return;

    try {
        const response = await fetch(`/preview-file/${filename}`);
        const data = await response.json();
        if (data.content) {
            elements.formPreview.innerHTML = `<pre>${escapeHtml(data.content)}</pre>`;
        } else {
            elements.formPreview.innerHTML = '<p>Σφάλμα φόρτωσης προεπισκόπησης</p>';
        }
    } catch (error) {
        elements.formPreview.innerHTML = '<p>Σφάλμα φόρτωσης προεπισκόπησης</p>';
    }
}

// Email Modal Functions
function openEmailModal(record) {
    const emailElements = {
        'email-record-id': record.id,
        'email-client-name': record.client_name || '',
        'email-email': record.email || '',
        'email-phone': record.phone || '',
        'email-company': record.company || '',
        'email-service': record.service_interest || '',
        'email-priority': record.priority || '',
        'email-message': record.message || '',
        'email-invoice-number': record.invoice_number || '',
        'email-amount': record.amount || '',
        'email-vat': record.vat || '',
        'email-total': record.total_amount || ''
    };

    Object.entries(emailElements).forEach(([elementId, value]) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = value;
        } else {
            console.warn(`Email element not found: ${elementId}`);
        }
    });

    const emailType = record.email_type || 'client_inquiry';
    setEmailType(emailType);

    loadEmailPreview(record.source_file, record);

    if (elements.emailModal) {
        elements.emailModal.classList.add('active');
    }
}

function setEmailType(type) {
    const typeBadge = elements.emailTypeBadge;
    const inquiryFields = document.querySelector('.client-inquiry-fields');
    const invoiceFields = document.querySelector('.invoice-email-fields');

    if (typeBadge) {
        if (type === 'invoice_notification') {
            typeBadge.textContent = 'ΤΙΜΟΛΟΓΙΟ EMAIL';
            typeBadge.className = 'type-badge invoice-notification';
        } else {
            typeBadge.textContent = 'ΑΙΤΗΜΑ ΠΕΛΑΤΗ';
            typeBadge.className = 'type-badge client-inquiry';
        }
    }

    if (inquiryFields && invoiceFields) {
        if (type === 'invoice_notification') {
            inquiryFields.style.display = 'none';
            invoiceFields.style.display = 'contents';
        } else {
            inquiryFields.style.display = 'contents';
            invoiceFields.style.display = 'none';
        }
    }
}

async function loadEmailPreview(filename, record) {
    try {
        const response = await fetch(`/preview-file/${filename}`);
        const emailData = await response.json();

        const headerElements = {
            'email-from': emailData.from || record.email || '',
            'email-to': emailData.to || '',
            'email-subject': emailData.subject || record.email_subject || '',
            'email-date': emailData.date || record.email_date || ''
        };

        Object.entries(headerElements).forEach(([elementId, value]) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        });

        const emailBody = document.getElementById('email-body');
        if (emailBody) {
            emailBody.textContent = emailData.body || record.message || '';
        }

    } catch (error) {
        const emailBody = document.getElementById('email-body');
        if (emailBody) {
            emailBody.textContent = 'Σφάλμα φόρτωσης προεπισκόπησης email';
        }
    }
}

// Invoice Modal Functions
function openInvoiceModal(record) {
    const invoiceElements = {
        'invoice-record-id': record.id,
        'invoice-number': record.invoice_number || '',
        'invoice-date': record.invoice_date || '',
        'invoice-client-name': record.client_name || '',
        'invoice-amount': record.amount || '',
        'invoice-vat': record.vat || '',
        'invoice-total': record.total_amount || '',
        'invoice-items': record.message || ''
    };

    Object.entries(invoiceElements).forEach(([elementId, value]) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = value;
        } else {
            console.warn(`Invoice element not found: ${elementId}`);
        }
    });

    loadInvoicePreview(record.source_file);

    if (elements.invoiceModal) {
        elements.invoiceModal.classList.add('active');
    }
}

async function loadInvoicePreview(filename) {
    if (!elements.invoicePreview) return;

    try {
        const response = await fetch(`/preview-file/${filename}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.content) {
            elements.invoicePreview.innerHTML = `
                <div class="preview-header">
                    <small>Τιμολόγιο: ${data.filename || filename}</small>
                </div>
                <iframe
                    srcdoc="${escapeHtml(data.content)}"
                    style="width: 100%; height: 420px; border: 1px solid #ddd; border-radius: 4px; background: white;"
                    sandbox="allow-same-origin">
                </iframe>
            `;
        } else {
            elements.invoicePreview.innerHTML = '<p class="preview-error">Δεν βρέθηκε περιεχόμενο τιμολογίου</p>';
        }
    } catch (error) {
        console.error('Error loading invoice preview:', error);
        elements.invoicePreview.innerHTML = `<p class="preview-error">Σφάλμα φόρτωσης προεπισκόπησης τιμολογίου: ${error.message}</p>`;
    }
}

// Modal Save Functions
async function saveRecord(modalType, action) {
    let formData = new FormData();
    let recordId;

    switch (modalType) {
        case 'form':
            if (elements.formEditForm) {
                formData = new FormData(elements.formEditForm);
            }
            recordId = document.getElementById('form-record-id')?.value;
            break;
        case 'email':
            if (elements.emailEditForm) {
                formData = new FormData(elements.emailEditForm);
            }
            recordId = document.getElementById('email-record-id')?.value;
            break;
        case 'invoice':
            if (elements.invoiceEditForm) {
                formData = new FormData(elements.invoiceEditForm);
            }
            recordId = document.getElementById('invoice-record-id')?.value;
            break;
    }

    if (!recordId) {
        showToast('Σφάλμα: Δεν βρέθηκε το ID της εγγραφής', 'error');
        return;
    }

    formData.append('action', action);

    const updatedData = {};
    formData.forEach((value, key) => {
        if (key !== 'action' && value.trim() !== '') {
            updatedData[key] = value.trim();
        }
    });

    const submitFormData = new FormData();
    submitFormData.append('action', action);
    submitFormData.append('updated_data', JSON.stringify(updatedData));

    try {
        const response = await fetch(`/update-record/${recordId}`, {
            method: 'POST',
            body: submitFormData
        });

        const result = await response.json();

        if (result.success) {
            closeAllModals();
            loadPendingData();
            updateStatistics();
        } else {
            showToast('Σφάλμα αποθήκευσης εγγραφής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

function closeAllModals() {
    [elements.formModal, elements.emailModal, elements.invoiceModal].forEach(modal => {
        if (modal) {
            modal.classList.remove('active');
        }
    });

    [elements.formEditForm, elements.emailEditForm, elements.invoiceEditForm].forEach(form => {
        if (form) {
            form.reset();
        }
    });

    currentEditRecord = null;
}

// Event Listeners Setup
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Upload functionality
    const selectFolderBtn = document.getElementById('select-folder-btn');
    const selectFilesBtn = document.getElementById('select-files-btn');
    const fileInput = document.getElementById('file-input');
    const fileInputFiles = document.getElementById('file-input-files');
    const uploadArea = document.getElementById('upload-area');

    if (selectFolderBtn && fileInput) {
        selectFolderBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }

    if (selectFilesBtn && fileInputFiles) {
        selectFilesBtn.addEventListener('click', () => {
            fileInputFiles.click();
        });
    }

    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelection);
    }

    if (fileInputFiles) {
        fileInputFiles.addEventListener('change', handleFileSelection);
    }

    // Drag and drop
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
    }

    // Control buttons
    const refreshAll = document.getElementById('refresh-all');
    const quickExport = document.getElementById('quick-export');
    const bulkApproveBtn = document.getElementById('bulk-approve-btn');
    const bulkRejectBtn = document.getElementById('bulk-reject-btn');
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    const deleteAllBtn = document.getElementById('delete-all-btn');
    const selectAllCheckbox = document.getElementById('select-all');
    const typeFilter = document.getElementById('type-filter');
    const searchInput = document.getElementById('search-input');

    if (refreshAll) {
        refreshAll.addEventListener('click', () => {
            loadPendingData();
            updateStatistics();
        });
    }

    if (quickExport) {
        quickExport.addEventListener('click', exportToExcel);
    }

    if (bulkApproveBtn) {
        bulkApproveBtn.addEventListener('click', () => bulkUpdateRecords('approved'));
    }

    if (bulkRejectBtn) {
        bulkRejectBtn.addEventListener('click', () => bulkUpdateRecords('rejected'));
    }

    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', bulkDeleteSelected);
    }

    if (deleteAllBtn) {
        console.log('Setting up delete all button listener');
        deleteAllBtn.addEventListener('click', deleteAllPending);
    } else {
        console.warn('Delete all button not found!');
    }

    // Select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', handleSelectAll);
    }

    // Filters
    if (typeFilter) {
        typeFilter.addEventListener('change', filterData);
    }

    if (searchInput) {
        searchInput.addEventListener('input', filterData);
    }

    // Setup table event delegation
    setupTableEventDelegation();
}

// Table Event Delegation Setup
function setupTableEventDelegation() {
    console.log('Setting up table event delegation...');
    if (elements.dataTbody) {
        // Remove any existing listener first
        elements.dataTbody.removeEventListener('click', handleTableActions);
        // Add the event listener
        elements.dataTbody.addEventListener('click', handleTableActions);
        console.log('Table event delegation set up successfully');
    } else {
        console.warn('dataTbody element not found for event delegation');
    }
}

// Centralized Table Action Handler
function handleTableActions(e) {
    console.log('Table action triggered:', e.target);
    
    const target = e.target;
    const button = target.closest('button');
    
    if (!button) {
        console.log('No button found in click target');
        return;
    }

    e.preventDefault();
    e.stopPropagation();

    const recordId = button.getAttribute('data-id');
    
    if (!recordId) {
        console.error('No record ID found on button:', button);
        showToast('Σφάλμα: Δεν βρέθηκε το ID της εγγραφής', 'error');
        return;
    }

    console.log('Button clicked with record ID:', recordId);

    // Handle different button types
    if (button.classList.contains('btn-edit')) {
        console.log('Edit button clicked for record:', recordId);
        openEditModal(recordId);
    } else if (button.classList.contains('btn-approve')) {
        console.log('Approve button clicked for record:', recordId);
        updateSingleRecord(recordId, 'approved');
    } else if (button.classList.contains('btn-reject')) {
        console.log('Reject button clicked for record:', recordId);
        updateSingleRecord(recordId, 'rejected');
    } else if (button.classList.contains('btn-delete')) {
        console.log('Delete button clicked for record:', recordId);
        deleteRecord(recordId);
    } else {
        console.log('Unknown button type clicked');
    }
}

// Progress and Processing Functions
function showProcessingPanel() {
    const processingPanel = document.getElementById('processing-panel');
    if (processingPanel) {
        processingPanel.style.display = 'block';
        switchTab('upload');
    }
}

function hideProcessingPanel() {
    const processingPanel = document.getElementById('processing-panel');
    if (processingPanel) {
        processingPanel.style.display = 'none';
    }
}

function updateProgress(percentage, message) {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const currentStatus = document.getElementById('current-status');

    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }
    if (progressText) {
        progressText.textContent = `${percentage}%`;
    }
    if (currentStatus) {
        currentStatus.textContent = message || 'Επεξεργασία...';
    }
}

function updateProcessingStats(processed, total) {
    const processedCount = document.getElementById('processed-count');
    const totalCount = document.getElementById('total-count');

    if (processedCount) {
        processedCount.textContent = processed;
    }
    if (totalCount) {
        totalCount.textContent = total;
    }
}

// Activity Timeline
function addActivityItem(message, type = 'info') {
    const activityTimeline = document.getElementById('activity-timeline');
    if (!activityTimeline) return;

    const iconMap = {
        success: 'fas fa-check',
        error: 'fas fa-exclamation-triangle',
        warning: 'fas fa-exclamation-circle',
        info: 'fas fa-info-circle'
    };

    const activityItem = document.createElement('div');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-icon ${type}">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-title">${message}</div>
            <div class="activity-time">${new Date().toLocaleTimeString('el-GR')}</div>
        </div>
    `;

    activityTimeline.insertBefore(activityItem, activityTimeline.firstChild);

    // Keep only last 10 items
    while (activityTimeline.children.length > 10) {
        activityTimeline.removeChild(activityTimeline.lastChild);
    }
}

function updateActivityTimeline() {
    const activityTimeline = document.getElementById('activity-timeline');
    if (activityTimeline) {
        activityTimeline.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon success">
                    <i class="fas fa-check"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">Σύστημα έτοιμο προς χρήση</div>
                    <div class="activity-time">${new Date().toLocaleTimeString('el-GR')}</div>
                </div>
            </div>
        `;
    }
}

// Statistics Functions
async function updateStatistics() {
    try {
        const response = await fetch('/statistics');
        const result = await response.json();

        if (result.success) {
            const stats = result.data;

            const currentStats = {
                total_records: stats.total_records || 0,
                pending: stats.pending || 0,
                approved: stats.approved || 0,
                rejected: stats.rejected || 0,
                total_amount: stats.total_amount || 0
            };

            const totalRecords = document.getElementById('total-records');
            const pendingRecords = document.getElementById('pending-records');
            const approvedRecords = document.getElementById('approved-records');
            const totalAmount = document.getElementById('total-amount');
            const totalFiles = document.getElementById('total-files');
            const processingFiles = document.getElementById('processing-files');
            const completedFiles = document.getElementById('completed-files');
            const pendingBadge = document.getElementById('pending-badge');
            const approvedBadge = document.getElementById('approved-badge');
            const lastUpdate = document.getElementById('last-update');

            if (totalRecords) totalRecords.textContent = currentStats.total_records;
            if (pendingRecords) pendingRecords.textContent = currentStats.pending;
            if (approvedRecords) approvedRecords.textContent = currentStats.approved;
            if (totalAmount) totalAmount.textContent = `€${currentStats.total_amount.toFixed(2)}`;
            if (totalFiles) totalFiles.textContent = currentStats.total_records;
            if (processingFiles) processingFiles.textContent = currentStats.pending;
            if (completedFiles) completedFiles.textContent = currentStats.approved;
            if (pendingBadge) pendingBadge.textContent = currentStats.pending;
            if (approvedBadge) approvedBadge.textContent = currentStats.approved;
            if (lastUpdate) lastUpdate.textContent = `${new Date().toLocaleTimeString('el-GR')}`;

            updateChangeIndicators(currentStats);
            previousStats = currentStats;
        }
    } catch (error) {
        console.error('Error updating statistics:', error);
    }
}

function updateChangeIndicators(currentStats) {
    if (previousStats) {
        const changes = {
            total_records: currentStats.total_records - previousStats.total_records,
            pending: currentStats.pending - previousStats.pending,
            approved: currentStats.approved - previousStats.approved,
            total_amount: currentStats.total_amount - previousStats.total_amount
        };

        updateMetricChange('total-records', changes.total_records, 'records');
        updateMetricChange('pending-records', changes.pending, 'pending');
        updateMetricChange('approved-records', changes.approved, 'approved');
        updateMetricChange('total-amount', changes.total_amount, 'amount');
    } else {
        setMetricChange('total-records', 'Σύστημα αρχικοποιημένο', 'positive');
        setMetricChange('pending-records', 'Απαιτεί έγκριση', 'warning');
        setMetricChange('approved-records', 'Έτοιμα για εξαγωγή', 'positive');
        setMetricChange('total-amount', 'Εγκεκριμένων τιμολογίων', 'info');
    }
}

function updateMetricChange(metricId, change, type) {
    const changeElement = document.querySelector(`#${metricId}`).closest('.metric-card').querySelector('.metric-change');
    if (!changeElement) return;

    if (change > 0) {
        const percentage = previousStats ? Math.round((change / previousStats[type === 'records' ? 'total_records' : type === 'pending' ? 'pending' : type === 'approved' ? 'approved' : 'total_amount']) * 100) : 0;
        setMetricChange(metricId, `+${percentage}% από προηγούμενη ενημέρωση`, 'positive');
    } else if (change < 0) {
        const percentage = previousStats ? Math.round((Math.abs(change) / previousStats[type === 'records' ? 'total_records' : type === 'pending' ? 'pending' : type === 'approved' ? 'approved' : 'total_amount']) * 100) : 0;
        setMetricChange(metricId, `-${percentage}% από προηγούμενη ενημέρωση`, 'negative');
    } else {
        setMetricChange(metricId, 'Δεν υπάρχει αλλαγή', 'neutral');
    }
}

function setMetricChange(metricId, text, type) {
    const changeElement = document.querySelector(`#${metricId}`).closest('.metric-card').querySelector('.metric-change');
    if (!changeElement) return;

    const textSpan = changeElement.querySelector('span');
    if (textSpan) {
        textSpan.textContent = text;
    }

    const icon = changeElement.querySelector('i');
    if (icon) {
        changeElement.classList.remove('positive', 'negative', 'neutral');
        icon.classList.remove('fa-arrow-up', 'fa-arrow-down', 'fa-minus', 'fa-exclamation-triangle', 'fa-info-circle', 'fa-calculator');

        switch (type) {
            case 'positive':
                changeElement.classList.add('positive');
                icon.classList.add('fa-arrow-up');
                break;
            case 'negative':
                changeElement.classList.add('negative');
                icon.classList.add('fa-arrow-down');
                break;
            case 'warning':
                icon.classList.add('fa-exclamation-triangle');
                break;
            case 'info':
                icon.classList.add('fa-calculator');
                break;
            default:
                changeElement.classList.add('neutral');
                icon.classList.add('fa-minus');
        }
    }
}

// Connection Status
function updateConnectionStatus(status, text) {
    const statusDot = document.getElementById('status-dot');
    const statusText = document.getElementById('status-text');

    if (statusDot) {
        statusDot.className = `fas fa-circle ${status}`;
    }
    if (statusText) {
        statusText.textContent = text;
    }
}

// File Upload Functions
function handleFileSelection(event) {
    const files = Array.from(event.target.files);
    if (files.length > 0) {
        uploadFiles(files);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.classList.add('dragover');
    }
}

function handleDragLeave(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.classList.remove('dragover');
    }
}

function handleDrop(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.classList.remove('dragover');
    }

    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
        uploadFiles(files);
    }
}

async function uploadFiles(files) {
    const formData = new FormData();

    const supportedFiles = files.filter(file => {
        const ext = file.name.toLowerCase().split('.').pop();
        return ['html', 'eml'].includes(ext);
    });

    if (supportedFiles.length === 0) {
        showToast('Δεν βρέθηκαν υποστηριζόμενα αρχεία', 'warning');
        return;
    }

    supportedFiles.forEach(file => {
        formData.append('files', file);
    });

    try {
        const response = await fetch('/upload-folder', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (!result.success) {
            showToast(`Σφάλμα: ${result.error}`, 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
        hideProcessingPanel();
    }
}

// Data Loading and Table Functions
async function loadPendingData() {
    try {
        const response = await fetch('/pending-data');
        const result = await response.json();

        if (result.success) {
            pendingData = result.data;
            renderDataTable();
            updateSelectionButtons();
        } else {
            showToast('Σφάλμα φόρτωσης δεδομένων', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

function renderDataTable() {
    const dataTbody = document.getElementById('data-tbody');
    if (!dataTbody) return;

    if (pendingData.length === 0) {
        dataTbody.innerHTML = `
            <tr class="no-data">
                <td colspan="12">
                    <div class="no-data-content">
                        <i class="fas fa-inbox"></i>
                        <h3>Δεν υπάρχουν δεδομένα</h3>
                        <p>Ανεβάστε αρχεία για να ξεκινήσετε την επεξεργασία</p>
                        <button class="btn-primary" onclick="switchTab('upload')">
                            <i class="fas fa-cloud-upload-alt"></i>
                            Φόρτωση Αρχείων
                        </button>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const tbody = pendingData.map(record => createTableRow(record)).join('');
    dataTbody.innerHTML = tbody;

    // Setup event listeners for the new table content
    attachTableEventListeners();
}

function attachTableEventListeners() {
    console.log('Attaching table event listeners...');
    
    // Set up checkbox listeners
    document.querySelectorAll('.record-checkbox').forEach(checkbox => {
        checkbox.removeEventListener('change', handleRecordSelection);
        checkbox.addEventListener('change', handleRecordSelection);
    });

    // Ensure event delegation is set up for table actions
    setupTableEventDelegation();
}

// Record Management Functions
async function updateSingleRecord(recordId, action) {
    if (!recordId || recordId === 'undefined') {
        showToast('Σφάλμα: Μη έγκυρο ID εγγραφής', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('action', action);

    try {
        const response = await fetch(`/update-record/${recordId}`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (!result.success) {
            showToast('Σφάλμα ενημέρωσης εγγραφής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

async function bulkUpdateRecords(action) {
    if (selectedRecords.size === 0) {
        showToast('Δεν έχετε επιλέξει εγγραφές', 'warning');
        return;
    }

    const recordIds = Array.from(selectedRecords);
    const formData = new FormData();
    formData.append('record_ids', JSON.stringify(recordIds));
    formData.append('action', action);

    try {
        const response = await fetch('/bulk-update', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            selectedRecords.clear();
            if (elements.selectAllCheckbox) elements.selectAllCheckbox.checked = false;
            updateSelectionButtons();
        } else {
            showToast('Σφάλμα μαζικής ενημέρωσης', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

// Delete Functions
async function deleteRecord(recordId) {
    console.log('deleteRecord called with ID:', recordId);
    
    if (!recordId || recordId === 'undefined' || recordId === 'null') {
        console.error('Invalid record ID:', recordId);
        showToast('Σφάλμα: Μη έγκυρο ID εγγραφής', 'error');
        return;
    }

    if (!confirm('Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εγγραφή;')) {
        return;
    }

    try {
        console.log('Sending DELETE request to:', `/delete-record/${recordId}`);
        
        const response = await fetch(`/delete-record/${recordId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log('Response status:', response.status);

        if (response.ok) {
            const result = await response.json();
            console.log('Delete response:', result);
            
            if (result.success) {
                showToast('Η εγγραφή διαγράφηκε επιτυχώς', 'success');
                loadPendingData();
                updateStatistics();
            } else {
                showToast(result.message || 'Σφάλμα διαγραφής εγγραφής', 'error');
            }
        } else {
            const errorText = await response.text();
            console.error('Delete failed with status:', response.status, errorText);
            showToast(`Σφάλμα διαγραφής: ${response.status} ${response.statusText}`, 'error');
        }
    } catch (error) {
        console.error('Network error during delete:', error);
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

async function bulkDeleteSelected() {
    console.log('bulkDeleteSelected called, selected records:', selectedRecords);
    
    if (selectedRecords.size === 0) {
        showToast('Δεν έχετε επιλέξει εγγραφές', 'warning');
        return;
    }

    if (!confirm(`Θέλετε σίγουρα να διαγράψετε ${selectedRecords.size} επιλεγμένες εγγραφές;`)) {
        return;
    }

    const recordIds = Array.from(selectedRecords);
    console.log('Bulk deleting records:', recordIds);
    
    const formData = new FormData();
    formData.append('record_ids', JSON.stringify(recordIds));

    try {
        const response = await fetch('/bulk-delete', {
            method: 'POST',
            body: formData
        });
        
        console.log('Bulk delete response status:', response.status);
        
        if (response.ok) {
            const result = await response.json();
            console.log('Bulk delete result:', result);
            
            if (result.success) {
                showToast(`Διαγράφηκαν ${result.count} εγγραφές επιτυχώς`, 'success');
                selectedRecords.clear();
                if (elements.selectAllCheckbox) elements.selectAllCheckbox.checked = false;
                updateSelectionButtons();
                loadPendingData();
                updateStatistics();
            } else {
                showToast(result.message || 'Σφάλμα μαζικής διαγραφής', 'error');
            }
        } else {
            const errorText = await response.text();
            console.error('Bulk delete failed:', response.status, errorText);
            showToast(`Σφάλμα μαζικής διαγραφής: ${response.status}`, 'error');
        }
    } catch (error) {
        console.error('Network error during bulk delete:', error);
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

async function deleteAllPending() {
    console.log('deleteAllPending called');
    
    if (!confirm('Θέλετε σίγουρα να διαγράψετε ΟΛΕΣ τις εκκρεμείς εγγραφές;')) {
        return;
    }

    try {
        console.log('Sending DELETE request to /delete-all-pending');
        
        const response = await fetch('/delete-all-pending', { 
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('Delete all response status:', response.status);
        
        if (response.ok) {
            const result = await response.json();
            console.log('Delete all result:', result);
            
            if (result.success) {
                showToast(`Διαγράφηκαν ${result.count} εκκρεμείς εγγραφές`, 'success');
                selectedRecords.clear();
                if (elements.selectAllCheckbox) elements.selectAllCheckbox.checked = false;
                updateSelectionButtons();
                loadPendingData();
                updateStatistics();
            } else {
                showToast(result.message || 'Σφάλμα διαγραφής όλων των εκκρεμών εγγραφών', 'error');
            }
        } else {
            const errorText = await response.text();
            console.error('Delete all failed:', response.status, errorText);
            showToast(`Σφάλμα διαγραφής όλων: ${response.status}`, 'error');
        }
    } catch (error) {
        console.error('Network error during delete all:', error);
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

// Selection Functions
function handleSelectAll() {
    if (!elements.selectAllCheckbox) return;

    const isChecked = elements.selectAllCheckbox.checked;
    const checkboxes = document.querySelectorAll('.record-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        if (isChecked) {
            selectedRecords.add(parseInt(checkbox.value));
        } else {
            selectedRecords.delete(parseInt(checkbox.value));
        }
    });

    updateSelectionButtons();
}

function handleRecordSelection(event) {
    const recordId = parseInt(event.target.value);

    if (event.target.checked) {
        selectedRecords.add(recordId);
    } else {
        selectedRecords.delete(recordId);
        if (elements.selectAllCheckbox) elements.selectAllCheckbox.checked = false;
    }

    updateSelectionButtons();
}

function updateSelectionButtons() {
    const hasSelection = selectedRecords.size > 0;

    if (elements.bulkActions) {
        elements.bulkActions.style.display = hasSelection ? 'flex' : 'none';
    }

    if (elements.selectedCount) {
        elements.selectedCount.textContent = `${selectedRecords.size} επιλεγμένα`;
    }

    if (elements.bulkApproveBtn) elements.bulkApproveBtn.disabled = !hasSelection;
    if (elements.bulkRejectBtn) elements.bulkRejectBtn.disabled = !hasSelection;
    if (elements.bulkDeleteBtn) elements.bulkDeleteBtn.disabled = !hasSelection;
}

// Filter Functions
function filterData() {
    const typeFilter = elements.typeFilter?.value || '';
    const searchTerm = elements.searchInput?.value.toLowerCase() || '';

    let filteredData = pendingData;

    if (typeFilter) {
        filteredData = filteredData.filter(record => record.type === typeFilter);
    }

    if (searchTerm) {
        filteredData = filteredData.filter(record => {
            const searchableText = [
                record.client_name,
                record.company,
                record.email,
                record.service_interest,
                record.source_file
            ].join(' ').toLowerCase();

            return searchableText.includes(searchTerm);
        });
    }

    renderFilteredTable(filteredData);
}

function renderFilteredTable(data) {
    if (!elements.dataTbody) return;

    if (data.length === 0) {
        elements.dataTbody.innerHTML = `
            <tr class="no-data">
                <td colspan="12">
                    <div class="no-data-content">
                        <i class="fas fa-search"></i>
                        <h3>Δεν βρέθηκαν αποτελέσματα</h3>
                        <p>Δοκιμάστε διαφορετικά κριτήρια αναζήτησης</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const tbody = data.map(record => createTableRow(record)).join('');
    elements.dataTbody.innerHTML = tbody;

    attachTableEventListeners();
}

// Export Functions
async function exportToExcel() {
    const formData = new FormData();
    formData.append('format_type', 'greek');

    try {
        const response = await fetch('/export-excel', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `extracted_data_${new Date().toISOString().slice(0, 10)}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showToast('Η εξαγωγή ολοκληρώθηκε επιτυχώς', 'success');
        } else {
            const result = await response.json();
            showToast(result.detail || 'Σφάλμα εξαγωγής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

// Toast Notification System
function showToast(message, type = 'info') {
    if (!elements.toastContainer) {
        console.warn('Toast container not found');
        return;
    }

    const toastId = 'toast_' + Date.now();
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.id = toastId;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="toast-content">
            <div class="toast-message">${message}</div>
        </div>
        <button class="toast-close" onclick="removeToast('${toastId}')">
            <i class="fas fa-times"></i>
        </button>
    `;

    elements.toastContainer.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => removeToast(toastId), 5000);
}

function removeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.style.animation = 'toastSlideOut 0.3s ease forwards';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

// Utility Functions
function translateType(type) {
    const translations = {
        'FORM': 'Φόρμα',
        'EMAIL': 'Email',
        'INVOICE': 'Τιμολόγιο'
    };
    return translations[type] || type;
}

function formatAmount(amount) {
    if (!amount) return '-';
    return `€${parseFloat(amount).toFixed(2)}`;
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR');
}

function truncateText(text, maxLength) {
    if (!text) return '-';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Global functions for onclick handlers
window.switchTab = switchTab;
window.removeToast = removeToast;

// Add CSS for toast slide out animation
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
