# Email Configuration and IMAP Integration

import imaplib
import email
import json
import ssl
import os
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from email.header import decode_header
import sqlite3
from cryptography.fernet import Fernet
import base64

@dataclass
class EmailSettings:
    """Email configuration settings"""
    server: str = ""
    port: int = 993
    username: str = ""
    password: str = ""  # Will be encrypted
    use_ssl: bool = True
    folders: List[str] = None
    check_interval: int = 300  # 5 minutes
    enabled: bool = False
    last_check: Optional[datetime] = None
    
    def __post_init__(self):
        if self.folders is None:
            self.folders = ["INBOX"]

class EmailCredentialManager:
    """Secure handling of email credentials"""
    
    def __init__(self, db_path: str = "automation_data.db"):
        self.db_path = db_path
        self.key = self._get_or_create_key()
        self.cipher = Fernet(self.key)
        self._init_credentials_table()
    
    def _get_or_create_key(self) -> bytes:
        """Get encryption key or create new one"""
        key_file = "email.key"
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def _init_credentials_table(self):
        """Initialize email credentials table"""
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS email_settings (
                    id INTEGER PRIMARY KEY,
                    server TEXT,
                    port INTEGER,
                    username TEXT,
                    encrypted_password TEXT,
                    use_ssl BOOLEAN,
                    folders TEXT,
                    check_interval INTEGER,
                    enabled BOOLEAN,
                    last_check TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            conn.commit()
        finally:
            conn.close()
    
    def save_settings(self, settings: EmailSettings) -> bool:
        """Save email settings with encrypted password"""
        try:
            encrypted_password = self.cipher.encrypt(settings.password.encode()).decode()
            folders_json = json.dumps(settings.folders)
            last_check_str = settings.last_check.isoformat() if settings.last_check else None
            
            conn = sqlite3.connect(self.db_path)
            try:
                # Check if settings exist
                cursor = conn.execute("SELECT id FROM email_settings LIMIT 1")
                existing = cursor.fetchone()
                
                if existing:
                    # Update existing
                    conn.execute('''
                        UPDATE email_settings SET
                        server=?, port=?, username=?, encrypted_password=?,
                        use_ssl=?, folders=?, check_interval=?, enabled=?, last_check=?
                        WHERE id=?
                    ''', (settings.server, settings.port, settings.username, encrypted_password,
                          settings.use_ssl, folders_json, settings.check_interval, 
                          settings.enabled, last_check_str, existing[0]))
                else:
                    # Insert new
                    conn.execute('''
                        INSERT INTO email_settings 
                        (server, port, username, encrypted_password, use_ssl, folders, 
                         check_interval, enabled, last_check)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (settings.server, settings.port, settings.username, encrypted_password,
                          settings.use_ssl, folders_json, settings.check_interval, 
                          settings.enabled, last_check_str))
                
                conn.commit()
                return True
            finally:
                conn.close()
        except Exception as e:
            print(f"Error saving email settings: {e}")
            return False
    
    def load_settings(self) -> Optional[EmailSettings]:
        """Load email settings with decrypted password"""
        try:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.execute('''
                    SELECT server, port, username, encrypted_password, use_ssl, 
                           folders, check_interval, enabled, last_check 
                    FROM email_settings ORDER BY id DESC LIMIT 1
                ''')
                row = cursor.fetchone()
                
                if not row:
                    return EmailSettings()
                
                server, port, username, encrypted_password, use_ssl, folders_json, check_interval, enabled, last_check_str = row
                
                # Decrypt password
                password = ""
                if encrypted_password:
                    try:
                        password = self.cipher.decrypt(encrypted_password.encode()).decode()
                    except Exception as e:
                        print(f"Error decrypting password: {e}")
                
                # Parse folders
                folders = json.loads(folders_json) if folders_json else ["INBOX"]
                
                # Parse last_check
                last_check = None
                if last_check_str:
                    try:
                        last_check = datetime.fromisoformat(last_check_str)
                    except Exception:
                        pass
                
                return EmailSettings(
                    server=server or "",
                    port=port or 993,
                    username=username or "",
                    password=password,
                    use_ssl=bool(use_ssl),
                    folders=folders,
                    check_interval=check_interval or 300,
                    enabled=bool(enabled),
                    last_check=last_check
                )
                
            finally:
                conn.close()
        except Exception as e:
            print(f"Error loading email settings: {e}")
            return EmailSettings()

class EmailFetcher:
    """Handles IMAP email fetching and processing"""
    
    def __init__(self, settings: EmailSettings, credential_manager: EmailCredentialManager):
        self.settings = settings
        self.credential_manager = credential_manager
        self.connection = None
        self.is_running = False
        self.thread = None
    
    def test_connection(self) -> Tuple[bool, str]:
        """Test email connection"""
        try:
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to server
            if self.settings.use_ssl:
                mail = imaplib.IMAP4_SSL(self.settings.server, self.settings.port, ssl_context=context)
            else:
                mail = imaplib.IMAP4(self.settings.server, self.settings.port)
            
            # Login
            mail.login(self.settings.username, self.settings.password)
            
            # List folders to verify connection
            status, folders = mail.list()
            if status != 'OK':
                return False, "Failed to list folders"
            
            mail.logout()
            return True, "Connection successful"
            
        except imaplib.IMAP4.error as e:
            return False, f"IMAP Error: {str(e)}"
        except Exception as e:
            return False, f"Connection Error: {str(e)}"
    
    def get_folder_list(self) -> List[str]:
        """Get list of available folders"""
        try:
            context = ssl.create_default_context()
            
            if self.settings.use_ssl:
                mail = imaplib.IMAP4_SSL(self.settings.server, self.settings.port, ssl_context=context)
            else:
                mail = imaplib.IMAP4(self.settings.server, self.settings.port)
            
            mail.login(self.settings.username, self.settings.password)
            
            status, folders = mail.list()
            if status == 'OK':
                folder_names = []
                for folder in folders:
                    # Parse folder name from IMAP response
                    folder_str = folder.decode() if isinstance(folder, bytes) else folder
                    # Extract folder name (usually after the last space and quotes)
                    import re
                    match = re.search(r'"([^"]*)"$', folder_str)
                    if match:
                        folder_names.append(match.group(1))
                    else:
                        # Fallback: take last part after space
                        parts = folder_str.split()
                        if parts:
                            folder_names.append(parts[-1].strip('"'))
                
                mail.logout()
                return folder_names
            
            mail.logout()
            return ["INBOX"]
            
        except Exception as e:
            print(f"Error getting folder list: {e}")
            return ["INBOX"]
    
    def fetch_new_emails(self) -> List[Dict]:
        """Fetch new emails since last check"""
        if not self.settings.enabled:
            return []
        
        try:
            context = ssl.create_default_context()
            
            if self.settings.use_ssl:
                mail = imaplib.IMAP4_SSL(self.settings.server, self.settings.port, ssl_context=context)
            else:
                mail = imaplib.IMAP4(self.settings.server, self.settings.port)
            
            mail.login(self.settings.username, self.settings.password)
            
            new_emails = []
            
            for folder in self.settings.folders:
                try:
                    # Select folder
                    status, _ = mail.select(folder)
                    if status != 'OK':
                        continue
                    
                    # Search for emails since last check
                    search_criteria = 'UNSEEN'  # Get unread emails
                    if self.settings.last_check:
                        # Format date for IMAP search
                        since_date = self.settings.last_check.strftime('%d-%b-%Y')
                        search_criteria = f'(SINCE {since_date})'
                    
                    status, email_ids = mail.search(None, search_criteria)
                    if status != 'OK':
                        continue
                    
                    # Process each email
                    for email_id in email_ids[0].split():
                        try:
                            email_data = self._fetch_email_content(mail, email_id, folder)
                            if email_data:
                                new_emails.append(email_data)
                        except Exception as e:
                            print(f"Error processing email {email_id}: {e}")
                            continue
                
                except Exception as e:
                    print(f"Error processing folder {folder}: {e}")
                    continue
            
            mail.logout()
            
            # Update last check time
            self.settings.last_check = datetime.now()
            self.credential_manager.save_settings(self.settings)
            
            return new_emails
            
        except Exception as e:
            print(f"Error fetching emails: {e}")
            return []
    
    def _fetch_email_content(self, mail, email_id, folder) -> Optional[Dict]:
        """Fetch and parse individual email"""
        try:
            # Fetch email
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return None
            
            # Parse email
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # Extract headers
            subject = self._decode_header(email_message.get('Subject', ''))
            from_addr = self._decode_header(email_message.get('From', ''))
            to_addr = self._decode_header(email_message.get('To', ''))
            date_str = email_message.get('Date', '')
            message_id = email_message.get('Message-ID', '')
            
            # Extract body
            body = self._extract_email_body(email_message)
            
            # Get attachments
            attachments = self._extract_attachments(email_message)
            
            return {
                'message_id': message_id,
                'folder': folder,
                'subject': subject,
                'from': from_addr,
                'to': to_addr,
                'date': date_str,
                'body': body,
                'attachments': attachments,
                'raw_email': raw_email.decode('utf-8', errors='ignore'),
                'fetched_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Error fetching email content: {e}")
            return None
    
    def _decode_header(self, header_value: str) -> str:
        """Decode email header safely"""
        if not header_value:
            return ""
        
        try:
            decoded_parts = decode_header(header_value)
            result = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            result += part.decode(encoding)
                        except (UnicodeDecodeError, LookupError):
                            # Try common encodings
                            for enc in ['utf-8', 'cp1253', 'iso-8859-7', 'latin1']:
                                try:
                                    result += part.decode(enc)
                                    break
                                except UnicodeDecodeError:
                                    continue
                            else:
                                result += part.decode('utf-8', errors='ignore')
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += part
            return result
        except Exception:
            return header_value
    
    def _extract_email_body(self, email_message) -> str:
        """Extract email body text"""
        try:
            body = ""
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue
                    
                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            payload = part.get_payload(decode=True)
                            if payload:
                                body = payload.decode(charset, errors='ignore')
                                break
                        except Exception:
                            continue
                    elif content_type == "text/html" and not body:
                        # Fallback to HTML if no plain text
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            payload = part.get_payload(decode=True)
                            if payload:
                                html_body = payload.decode(charset, errors='ignore')
                                # Simple HTML to text conversion
                                from bs4 import BeautifulSoup
                                soup = BeautifulSoup(html_body, 'html.parser')
                                body = soup.get_text(separator=' ', strip=True)
                        except Exception:
                            continue
            else:
                # Single part message
                charset = email_message.get_content_charset() or 'utf-8'
                try:
                    payload = email_message.get_payload(decode=True)
                    if payload:
                        body = payload.decode(charset, errors='ignore')
                except Exception:
                    body = str(email_message.get_payload())
            
            return body
            
        except Exception as e:
            print(f"Error extracting email body: {e}")
            return ""
    
    def _extract_attachments(self, email_message) -> List[Dict]:
        """Extract attachment information"""
        attachments = []
        
        try:
            for part in email_message.walk():
                content_disposition = str(part.get("Content-Disposition"))
                
                if "attachment" in content_disposition:
                    filename = part.get_filename()
                    if filename:
                        filename = self._decode_header(filename)
                        content_type = part.get_content_type()
                        size = len(part.get_payload(decode=True) or b"")
                        
                        attachments.append({
                            'filename': filename,
                            'content_type': content_type,
                            'size': size
                        })
        
        except Exception as e:
            print(f"Error extracting attachments: {e}")
        
        return attachments
    
    def start_monitoring(self):
        """Start email monitoring in background thread"""
        if self.is_running:
            return
        
        self.is_running = True
        self.thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.thread.start()
        print("Email monitoring started")
    
    def stop_monitoring(self):
        """Stop email monitoring"""
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=5)
        print("Email monitoring stopped")
    
    def _monitoring_loop(self):
        """Background email monitoring loop"""
        while self.is_running:
            try:
                if self.settings.enabled:
                    print(f"Checking for new emails at {datetime.now()}")
                    new_emails = self.fetch_new_emails()
                    
                    if new_emails:
                        print(f"Found {len(new_emails)} new emails")
                        # Process new emails through the existing pipeline
                        self._process_new_emails(new_emails)
                    
                    # Wait for next check
                    time.sleep(self.settings.check_interval)
                else:
                    # If disabled, check every minute for settings changes
                    time.sleep(60)
                    # Reload settings to check if enabled
                    self.settings = self.credential_manager.load_settings()
                    
            except Exception as e:
                print(f"Error in email monitoring loop: {e}")
                time.sleep(60)  # Wait before retrying
    
    def _process_new_emails(self, emails: List[Dict]):
        """Process new emails through extraction pipeline"""
        from email_processor import EmailProcessor
        from database import save_extracted_data, get_db
        
        processor = EmailProcessor()
        
        for email_data in emails:
            try:
                # Create temporary EML file for processing
                temp_filename = f"temp_email_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{email_data.get('message_id', 'unknown').replace('<', '').replace('>', '').replace('@', '_')}.eml"
                temp_path = os.path.join("temp", temp_filename)
                
                # Ensure temp directory exists
                os.makedirs("temp", exist_ok=True)
                
                # Write email to temp file
                with open(temp_path, 'w', encoding='utf-8') as f:
                    f.write(email_data['raw_email'])
                
                # Process email
                extracted_data = processor.parse_eml_file(temp_path)
                
                if 'error' not in extracted_data:
                    # Add source info
                    extracted_data['source_file'] = f"Email: {email_data['subject']}"
                    extracted_data['raw_data'] = json.dumps({
                        **extracted_data,
                        'original_email': {
                            'subject': email_data['subject'],
                            'from': email_data['from'],
                            'date': email_data['date'],
                            'folder': email_data['folder']
                        }
                    }, ensure_ascii=False)
                    
                    # Save to database
                    db = next(get_db())
                    try:
                        save_extracted_data(db, extracted_data)
                        print(f"Successfully processed email: {email_data['subject']}")
                    finally:
                        db.close()
                
                # Clean up temp file
                try:
                    os.remove(temp_path)
                except Exception:
                    pass
                    
            except Exception as e:
                print(f"Error processing email {email_data.get('subject', 'Unknown')}: {e}")

# Global email fetcher instance
email_fetcher_instance = None

def get_email_fetcher() -> Optional[EmailFetcher]:
    """Get the global email fetcher instance"""
    return email_fetcher_instance

def initialize_email_monitoring():
    """Initialize email monitoring system"""
    global email_fetcher_instance
    
    try:
        credential_manager = EmailCredentialManager()
        settings = credential_manager.load_settings()
        
        if settings and settings.enabled:
            email_fetcher_instance = EmailFetcher(settings, credential_manager)
            email_fetcher_instance.start_monitoring()
            print("Email monitoring initialized and started")
        else:
            print("Email monitoring not enabled")
            
    except Exception as e:
        print(f"Error initializing email monitoring: {e}")

def restart_email_monitoring():
    """Restart email monitoring with new settings"""
    global email_fetcher_instance
    
    # Stop existing monitoring
    if email_fetcher_instance:
        email_fetcher_instance.stop_monitoring()
    
    # Start new monitoring
    initialize_email_monitoring()