from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, UploadFile, File, Form, Depends, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, Response
from sqlalchemy.orm import Session
import json
import asyncio
import os
import shutil
import tempfile
import email
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
import re
import codecs

from datetime import datetime

from database import create_tables, get_db, save_extracted_data, get_all_pending_data, update_record_status, get_approved_data, delete_record, check_duplicate_file, delete_records, delete_all_pending
from extractors import DataExtractors
from export_manager import ExportManager

from email_settings import (
    EmailSettings, EmailCredentialManager, EmailFetcher,
    initialize_email_monitoring, restart_email_monitoring,
    get_email_fetcher
)

credential_manager = EmailCredentialManager()

app = FastAPI(title="Automation Data Extraction System")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create database tables
create_tables()

# Initialize services
data_extractors = DataExtractors()
export_manager = ExportManager()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove broken connections
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the main application page"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for keepalive
            await manager.send_personal_message(f"Received: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

def get_email_body_with_encoding(msg) -> str:
    """Extract email body text with robust encoding handling and HTML fallback"""
    try:
        def decode_payload(part) -> str:
            payload = part.get_payload(decode=True)
            if not payload:
                return ""
            # Try declared charset first
            declared = (part.get_content_charset() or '').strip() or 'utf-8'
            try:
                return payload.decode(declared)
            except (UnicodeDecodeError, LookupError):
                # Try common Greek encodings
                for enc in ['utf-8', 'cp1253', 'iso-8859-7', 'latin1']:
                    try:
                        return payload.decode(enc)
                    except UnicodeDecodeError:
                        continue
                # Last resort
                return payload.decode('utf-8', errors='ignore')

        def normalize_unicode_escapes(text: str) -> str:
            # If the text contains literal \uXXXX sequences, convert them to real Unicode
            try:
                if re.search(r"\\u[0-9a-fA-F]{4}", text):
                    return codecs.decode(text, 'unicode_escape')
            except Exception:
                pass
            return text

        if msg.is_multipart():
            text_html_candidate = None
            for part in msg.walk():
                ctype = part.get_content_type()
                if ctype == 'text/plain':
                    return normalize_unicode_escapes(decode_payload(part))
                if ctype == 'text/html' and text_html_candidate is None:
                    text_html_candidate = decode_payload(part)
            # Fallback to HTML converted to text
            if text_html_candidate:
                try:
                    soup = BeautifulSoup(text_html_candidate, 'html.parser')
                    return normalize_unicode_escapes(soup.get_text(separator=' ', strip=True))
                except Exception:
                    return normalize_unicode_escapes(text_html_candidate)
        else:
            ctype = msg.get_content_type() or 'text/plain'
            if ctype == 'text/plain':
                return normalize_unicode_escapes(decode_payload(msg))
            if ctype == 'text/html':
                html_text = decode_payload(msg)
                try:
                    soup = BeautifulSoup(html_text, 'html.parser')
                    return normalize_unicode_escapes(soup.get_text(separator=' ', strip=True))
                except Exception:
                    return normalize_unicode_escapes(html_text)
        return ""
    except Exception as e:
        print(f"Error extracting email body: {str(e)}")
        return "Σφάλμα εξαγωγής περιεχομένου email"

def decode_header_safe(header_value: str) -> str:
    """Safely decode email headers that might contain encoded words"""
    try:
        from email.header import decode_header
        decoded_parts = decode_header(header_value)
        result = ""
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                if encoding:
                    try:
                        result += part.decode(encoding)
                    except (UnicodeDecodeError, LookupError):
                        # Try common encodings
                        for enc in ['utf-8', 'cp1253', 'iso-8859-7', 'latin1']:
                            try:
                                result += part.decode(enc)
                                break
                            except UnicodeDecodeError:
                                continue
                        else:
                            result += part.decode('utf-8', errors='ignore')
                else:
                    result += part.decode('utf-8', errors='ignore')
            else:
                result += part
        return result
    except Exception as e:
        print(f"Error decoding header: {str(e)}")
        return header_value

@app.get("/preview-file/{filename}")
async def preview_file(filename: str):
    try:
        print(f"Preview request for file: {filename}")  # Debug log
        base_dir = "uploads"
        safe_filename = os.path.basename(filename)
        if safe_filename != filename:
            print(f"Sanitized filename from '{filename}' to '{safe_filename}' to prevent path traversal")
        file_path = os.path.join(base_dir, safe_filename)

        if not os.path.exists(file_path):
            print(f"File not found in uploads: {file_path}")  # Debug log
            raise HTTPException(status_code=404, detail=f"File not found: {safe_filename}")

        file_ext = os.path.splitext(file_path)[1].lower()
        print(f"File extension: {file_ext}")  # Debug log

        content_to_send = {}

        if file_ext == '.html':
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                content_to_send = {"content": html_content, "type": "html", "filename": filename}
                print(f"Successfully read HTML file, length: {len(html_content)}")  # Debug log
            except UnicodeDecodeError:
                # Fallback encodings
                for encoding in ['cp1253', 'iso-8859-7', 'latin1']:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            html_content = f.read()
                        content_to_send = {"content": html_content, "type": "html", "filename": filename}
                        print(f"Successfully read HTML file with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue
                if not content_to_send:
                    raise HTTPException(status_code=500, detail="Could not decode file with any encoding")

        elif file_ext == '.eml':
            try:
                # Try UTF-8 first
                with open(file_path, 'r', encoding='utf-8') as f:
                    eml_content = f.read()
            except UnicodeDecodeError:
                eml_content = None
                # Fallback encodings
                for encoding in ['cp1253', 'iso-8859-7', 'latin1']:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            eml_content = f.read()
                        break
                    except UnicodeDecodeError:
                        continue
                if not eml_content:
                    raise HTTPException(status_code=500, detail="Could not decode email with any encoding")

            msg = email.message_from_string(eml_content)
            email_body = get_email_body_with_encoding(msg)

            content_to_send = {
                'from': decode_header_safe(msg.get('From', '')),
                'to': decode_header_safe(msg.get('To', '')),
                'subject': decode_header_safe(msg.get('Subject', '')),
                'date': msg.get('Date', ''),
                'body': email_body,
                'type': 'email',
                'filename': filename
            }
            print(f"Successfully parsed email: {content_to_send['subject']}")  # Debug log

        else:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_ext}")

        # CORRECTED JSON RESPONSE with ensure_ascii=False
        json_content = json.dumps(content_to_send, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in preview_file: {str(e)}")  # Debug log
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/email-settings")
async def get_email_settings():
    """Get current email settings (without password)"""
    try:
        settings = credential_manager.load_settings()
        
        # Return settings without password for security
        settings_dict = {
            "server": settings.server,
            "port": settings.port,
            "username": settings.username,
            "use_ssl": settings.use_ssl,
            "folders": settings.folders,
            "check_interval": settings.check_interval,
            "enabled": settings.enabled,
            "last_check": settings.last_check.isoformat() if settings.last_check else None
        }
        
        json_content = json.dumps({"success": True, "settings": settings_dict}, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/email-settings")
async def save_email_settings(
    server: str = Form(...),
    port: int = Form(...),
    username: str = Form(...),
    password: str = Form(...),
    use_ssl: bool = Form(True),
    folders: str = Form("INBOX"),  # Comma-separated folder names
    check_interval: int = Form(300),
    enabled: bool = Form(False)
):
    """Save email settings"""
    try:
        # Parse folders
        folder_list = [f.strip() for f in folders.split(",") if f.strip()]
        if not folder_list:
            folder_list = ["INBOX"]
        
        # Create settings object
        settings = EmailSettings(
            server=server,
            port=port,
            username=username,
            password=password,
            use_ssl=use_ssl,
            folders=folder_list,
            check_interval=check_interval,
            enabled=enabled
        )
        
        # Save settings
        success = credential_manager.save_settings(settings)
        
        if success:
            # Restart email monitoring if enabled
            if enabled:
                restart_email_monitoring()
            
            return {"success": True, "message": "Email settings saved successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to save email settings")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/test-email-connection")
async def test_email_connection(
    server: str = Form(...),
    port: int = Form(...),
    username: str = Form(...),
    password: str = Form(...),
    use_ssl: bool = Form(True)
):
    """Test email connection"""
    try:
        settings = EmailSettings(
            server=server,
            port=port,
            username=username,
            password=password,
            use_ssl=use_ssl
        )
        
        fetcher = EmailFetcher(settings, credential_manager)
        success, message = fetcher.test_connection()
        
        return {"success": success, "message": message}
        
    except Exception as e:
        return {"success": False, "message": str(e)}

@app.get("/email-folders")
async def get_email_folders():
    """Get available email folders"""
    try:
        settings = credential_manager.load_settings()
        
        if not settings.server or not settings.username or not settings.password:
            raise HTTPException(status_code=400, detail="Email settings not configured")
        
        fetcher = EmailFetcher(settings, credential_manager)
        folders = fetcher.get_folder_list()
        
        json_content = json.dumps({"success": True, "folders": folders}, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/email-monitoring/start")
async def start_email_monitoring():
    """Start email monitoring"""
    try:
        fetcher = get_email_fetcher()
        if fetcher:
            fetcher.start_monitoring()
            message = "Email monitoring started"
        else:
            restart_email_monitoring()
            message = "Email monitoring initialized and started"
        
        return {"success": True, "message": message}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/email-monitoring/stop")
async def stop_email_monitoring():
    """Stop email monitoring"""
    try:
        fetcher = get_email_fetcher()
        if fetcher:
            fetcher.stop_monitoring()
            return {"success": True, "message": "Email monitoring stopped"}
        else:
            return {"success": True, "message": "Email monitoring was not running"}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/email-monitoring/status")
async def get_email_monitoring_status():
    """Get email monitoring status"""
    try:
        fetcher = get_email_fetcher()
        settings = credential_manager.load_settings()
        
        status = {
            "enabled": settings.enabled if settings else False,
            "running": fetcher.is_running if fetcher else False,
            "last_check": settings.last_check.isoformat() if settings and settings.last_check else None,
            "check_interval": settings.check_interval if settings else 300,
            "configured": bool(settings.server and settings.username) if settings else False
        }
        
        json_content = json.dumps({"success": True, "status": status}, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/email-monitoring/check-now")
async def check_emails_now():
    """Manually trigger email check"""
    try:
        fetcher = get_email_fetcher()
        if not fetcher:
            raise HTTPException(status_code=400, detail="Email monitoring not configured")
        
        new_emails = fetcher.fetch_new_emails()
        
        await manager.broadcast(json.dumps({
            "type": "email_check_completed",
            "message": f"Βρέθηκαν {len(new_emails)} νέα emails",
            "count": len(new_emails)
        }, ensure_ascii=False))
        
        return {"success": True, "message": f"Found {len(new_emails)} new emails", "count": len(new_emails)}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload-folder")
async def upload_folder(files: List[UploadFile] = File(...), db: Session = Depends(get_db)):
    """Upload and process multiple files as a folder"""

    try:
        # Create temporary directory for uploaded files
        temp_dir = tempfile.mkdtemp()
        uploaded_files = []

        # Send initial status
        await manager.broadcast(json.dumps({
            "type": "upload_started",
            "message": f"Ξεκίνησε η επεξεργασία {len(files)} αρχείων...",
            "total_files": len(files)
        }))

        # Save uploaded files
        for i, file in enumerate(files):
            if file.filename:
                file_path = os.path.join(temp_dir, file.filename)

                # Create subdirectories if needed
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)

                # Persist a copy under the uploads directory for future previews
                try:
                    uploads_dir = "uploads"
                    os.makedirs(uploads_dir, exist_ok=True)
                    persistent_path = os.path.join(uploads_dir, os.path.basename(file_path))
                    shutil.copyfile(file_path, persistent_path)
                except Exception as copy_err:
                    print(f"Warning: failed to persist file to uploads: {copy_err}")

                uploaded_files.append(file_path)

                # Send progress update
                await manager.broadcast(json.dumps({
                    "type": "upload_progress",
                    "message": f"Αποθηκεύτηκε αρχείο: {file.filename}",
                    "progress": int((i + 1) / len(files) * 50)  # 50% for upload
                }))

        # Process files
        await manager.broadcast(json.dumps({
            "type": "processing_started",
            "message": "Ξεκίνησε η εξαγωγή δεδομένων...",
            "progress": 50
        }))

        extraction_results = []
        processed_count = 0
        duplicates_found = 0

        for file_path in uploaded_files:
            try:
                print(f"Processing file: {file_path}")  # Debug log

                # Check for duplicates first
                duplicate_check = check_duplicate_file(db, file_path)

                if duplicate_check["is_duplicate"]:
                    duplicates_found += 1
                    existing = duplicate_check["existing_record"]

                    print(f"Duplicate found: {file_path} - {duplicate_check['duplicate_type']}")

                    extraction_results.append({
                        "filename": os.path.basename(file_path),
                        "status": "duplicate",
                        "duplicate_type": duplicate_check["duplicate_type"],
                        "existing_record": existing,
                        "message": f"Το αρχείο υπάρχει ήδη ({duplicate_check['duplicate_type']})"
                    })

                    # Send real-time update for duplicate
                    await manager.broadcast(json.dumps({
                        "type": "file_duplicate",
                        "message": f"Διπλότυπο: {os.path.basename(file_path)}",
                        "data": {
                            "filename": os.path.basename(file_path),
                            "duplicate_type": duplicate_check["duplicate_type"],
                            "existing_id": existing["id"],
                            "existing_status": existing["status"]
                        }
                    }))

                    processed_count += 1
                    progress = 50 + int((processed_count / len(uploaded_files)) * 50)

                    await manager.broadcast(json.dumps({
                        "type": "processing_progress",
                        "progress": progress,
                        "processed": processed_count,
                        "total": len(uploaded_files)
                    }))

                    continue

                # Extract data from file
                extracted_data = data_extractors.extract_from_file(file_path)
                print(f"Extracted data: {extracted_data}")  # Debug log

                if 'error' not in extracted_data:
                    # Ensure we have the required fields for database
                    if 'type' not in extracted_data:
                        print(f"Warning: No type field in extracted data for {file_path}")
                        extracted_data['type'] = 'UNKNOWN'

                    # Add file hash to extracted data
                    if duplicate_check.get("file_hash"):
                        extracted_data['file_hash'] = duplicate_check["file_hash"]

                    # Save to database
                    extracted_data['raw_data'] = json.dumps(extracted_data, ensure_ascii=False)
                    print(f"Saving to database: {extracted_data}")  # Debug log

                    try:
                        db_record = save_extracted_data(db, extracted_data)
                        print(f"Saved record with ID: {db_record.id}")  # Debug log

                        extraction_results.append({
                            "id": db_record.id,
                            "filename": os.path.basename(file_path),
                            "type": extracted_data.get('type', 'UNKNOWN'),
                            "status": "success"
                        })

                        # Send real-time update
                        await manager.broadcast(json.dumps({
                            "type": "file_processed",
                            "message": f"Επεξεργάστηκε: {os.path.basename(file_path)}",
                            "data": {
                                "id": db_record.id,
                                "filename": os.path.basename(file_path),
                                "type": extracted_data.get('type'),
                                "client_name": extracted_data.get('client_name'),
                                "company": extracted_data.get('company'),
                                "amount": extracted_data.get('total_amount') or extracted_data.get('amount')
                            }
                        }))

                    except Exception as db_error:
                        print(f"Database error for {file_path}: {str(db_error)}")
                        extraction_results.append({
                            "filename": os.path.basename(file_path),
                            "status": "error",
                            "error": f"Database error: {str(db_error)}"
                        })

                else:
                    print(f"Extraction error for {file_path}: {extracted_data['error']}")
                    extraction_results.append({
                        "filename": os.path.basename(file_path),
                        "status": "error",
                        "error": extracted_data['error']
                    })

                processed_count += 1
                progress = 50 + int((processed_count / len(uploaded_files)) * 50)

                await manager.broadcast(json.dumps({
                    "type": "processing_progress",
                    "progress": progress,
                    "processed": processed_count,
                    "total": len(uploaded_files)
                }))

            except Exception as e:
                print(f"General error processing {file_path}: {str(e)}")
                extraction_results.append({
                    "filename": os.path.basename(file_path),
                    "status": "error",
                    "error": str(e)
                })
                processed_count += 1

        # Cleanup temporary directory
        shutil.rmtree(temp_dir)

        # Send completion message
        successful_extractions = [r for r in extraction_results if r.get("status") == "success"]
        duplicate_files = [r for r in extraction_results if r.get("status") == "duplicate"]
        error_files = [r for r in extraction_results if r.get("status") == "error"]

        completion_message = f"Ολοκληρώθηκε! Επεξεργάστηκαν {len(successful_extractions)} αρχεία"
        if duplicate_files:
            completion_message += f", {len(duplicate_files)} διπλότυπα παραλείφθηκαν"
        if error_files:
            completion_message += f", {len(error_files)} σφάλματα"

        await manager.broadcast(json.dumps({
            "type": "processing_completed",
            "message": completion_message,
            "progress": 100,
            "results": extraction_results,
            "summary": {
                "processed": len(successful_extractions),
                "duplicates": len(duplicate_files),
                "errors": len(error_files),
                "total": len(files)
            }
        }))

        return {
            "success": True,
            "message": completion_message,
            "results": extraction_results,
            "summary": {
                "processed": len(successful_extractions),
                "duplicates": len(duplicate_files),
                "errors": len(error_files)
            }
        }

    except Exception as e:
        await manager.broadcast(json.dumps({
            "type": "error",
            "message": f"Σφάλμα επεξεργασίας: {str(e)}"
        }))

        return {
            "success": False,
            "error": str(e)
        }

@app.get("/pending-data")
async def get_pending_data(db: Session = Depends(get_db)):
    """Get all pending data for review"""
    try:
        pending_records = get_all_pending_data(db)

        results = []
        for record in pending_records:
            record_dict = {
                "id": record.id,
                "type": record.type,
                "source_file": record.source_file,
                "client_name": record.client_name,
                "email": record.email,
                "phone": record.phone,
                "company": record.company,
                "service_interest": record.service_interest,
                "amount": record.amount,
                "vat": record.vat,
                "total_amount": record.total_amount,
                "invoice_number": record.invoice_number,
                "priority": record.priority,
                "message": record.message,
                "email_type": record.email_type,
                "date_extracted": record.date_extracted.isoformat() if record.date_extracted else None
            }
            results.append(record_dict)

        # CORRECTED JSON RESPONSE with ensure_ascii=False
        json_content = json.dumps({"success": True, "data": results}, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/update-record/{record_id}")
async def update_record(
    record_id: int,
    action: str = Form(...),  # approve, reject, edit
    updated_data: str = Form(None),  # JSON string of updated data
    db: Session = Depends(get_db)
):
    """Update record status and optionally modify data"""
    try:
        update_dict = {}

        if updated_data:
            try:
                update_dict = json.loads(updated_data)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Μη έγκυρα δεδομένα JSON")

        # Update record
        updated_record = update_record_status(db, record_id, action, update_dict)

        if not updated_record:
            raise HTTPException(status_code=404, detail="Δεν βρέθηκε η εγγραφή")

        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "record_updated",
            "message": f"Η εγγραφή {record_id} {'εγκρίθηκε' if action == 'approved' else 'απορρίφθηκε' if action == 'rejected' else 'ενημερώθηκε'}",
            "record_id": record_id,
            "action": action
        }))

        return {
            "success": True,
            "message": f"Η εγγραφή ενημερώθηκε επιτυχώς"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/bulk-update")
async def bulk_update_records(
    record_ids: str = Form(...),
    action: str = Form(...),
    db: Session = Depends(get_db)
):
    """Update multiple records at once"""
    try:
        ids = json.loads(record_ids)

        updated_count = 0
        for record_id in ids:
            result = update_record_status(db, record_id, action)
            if result:
                updated_count += 1

        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "bulk_update",
            "message": f"Ενημερώθηκαν {updated_count} εγγραφές",
            "count": updated_count,
            "action": action
        }))

        return {
            "success": True,
            "message": f"Ενημερώθηκαν {updated_count} εγγραφές"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/delete-record/{record_id}")
async def delete_record_endpoint(record_id: int, db: Session = Depends(get_db)):
    """Delete a record"""
    print(f"DELETE: Received request to delete record ID: {record_id}")
    
    # Validate record_id
    if not record_id or record_id <= 0:
        print(f"DELETE: Invalid record ID: {record_id}")
        raise HTTPException(status_code=400, detail="Μη έγκυρο ID εγγραφής")
    
    try:
        success = delete_record(db, record_id)

        if not success:
            print(f"DELETE: Record ID {record_id} not found or could not be deleted.")
            raise HTTPException(status_code=404, detail="Δεν βρέθηκε η εγγραφή")

        print(f"DELETE: Successfully deleted record ID: {record_id}")
        
        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "record_deleted",
            "message": f"Διαγράφηκε η εγγραφή {record_id}",
            "record_id": record_id
        }, ensure_ascii=False))

        return {
            "success": True,
            "message": "Η εγγραφή διαγράφηκε επιτυχώς"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"DELETE ERROR: Failed to delete record ID {record_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Σφάλμα διαγραφής: {str(e)}")


@app.post("/bulk-delete")
async def bulk_delete_records(
    record_ids: str = Form(...),
    db: Session = Depends(get_db)
):
    """Delete multiple records at once"""
    print(f"BULK DELETE: Received form data - record_ids: {record_ids}")
    
    try:
        # Parse the JSON string
        ids = json.loads(record_ids)
        print(f"BULK DELETE: Parsed IDs: {ids}")
        
        # Validate that we have IDs
        if not ids or not isinstance(ids, list):
            print("BULK DELETE: No valid IDs provided")
            raise HTTPException(status_code=400, detail="Δεν δόθηκαν έγκυρα IDs")
        
        # Validate each ID
        valid_ids = []
        for id_val in ids:
            try:
                valid_id = int(id_val)
                if valid_id > 0:
                    valid_ids.append(valid_id)
            except (ValueError, TypeError):
                print(f"BULK DELETE: Invalid ID: {id_val}")
                continue
        
        if not valid_ids:
            print("BULK DELETE: No valid IDs after validation")
            raise HTTPException(status_code=400, detail="Δεν βρέθηκαν έγκυρα IDs")
        
        print(f"BULK DELETE: Valid IDs to delete: {valid_ids}")
        deleted_count = delete_records(db, valid_ids)

        print(f"BULK DELETE: Successfully deleted {deleted_count} records.")
        
        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "bulk_deleted",
            "message": f"Διαγράφηκαν {deleted_count} εγγραφές",
            "count": deleted_count
        }, ensure_ascii=False))

        return {
            "success": True,
            "message": f"Διαγράφηκαν {deleted_count} εγγραφές",
            "count": deleted_count
        }

    except json.JSONDecodeError as e:
        print(f"BULK DELETE: JSON decode error: {str(e)}")
        raise HTTPException(status_code=400, detail="Μη έγκυρα δεδομένα JSON")
    except HTTPException:
        raise
    except Exception as e:
        print(f"BULK DELETE ERROR: Failed to delete records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Σφάλμα μαζικής διαγραφής: {str(e)}")


@app.delete("/delete-all-pending")
async def delete_all_pending_endpoint(db: Session = Depends(get_db)):
    """Delete all pending records"""
    print("DELETE ALL PENDING: Received request to delete all pending records.")
    
    try:
        deleted_count = delete_all_pending(db)

        print(f"DELETE ALL PENDING: Successfully deleted {deleted_count} pending records.")
        
        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "pending_cleared",
            "message": f"Διαγράφηκαν {deleted_count} εκκρεμείς εγγραφές",
            "count": deleted_count
        }, ensure_ascii=False))

        return {
            "success": True,
            "message": f"Διαγράφηκαν {deleted_count} εκκρεμείς εγγραφές",
            "count": deleted_count
        }
        
    except Exception as e:
        print(f"DELETE ALL PENDING ERROR: Failed to delete all pending records: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Σφάλμα διαγραφής όλων: {str(e)}")


@app.post("/export-excel")
async def export_to_excel(
    format_type: str = Form("template"),
    db: Session = Depends(get_db)
):
    """Export approved data to Excel"""
    try:
        approved_records = get_approved_data(db)

        if not approved_records:
            raise HTTPException(status_code=400, detail="Δεν υπάρχουν εγκεκριμένες εγγραφές για εξαγωγή")

        template_format = format_type == "template"
        filepath = export_manager.export_to_excel(approved_records, template_format)

        # Send notification
        await manager.broadcast(json.dumps({
            "type": "export_completed",
            "message": f"Η εξαγωγή ολοκληρώθηκε. Εξήχθησαν {len(approved_records)} εγγραφές.",
            "filename": os.path.basename(filepath)
        }))

        return FileResponse(
            filepath,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=os.path.basename(filepath)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/export-summary")
async def export_summary_report(db: Session = Depends(get_db)):
    """Export summary report"""
    try:
        from database import ExtractedData
        all_records = db.query(ExtractedData).all()

        filepath = export_manager.export_summary_report(all_records)

        return FileResponse(
            filepath,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=os.path.basename(filepath)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/debug/all-data")
async def get_all_data_debug(db: Session = Depends(get_db)):
    """Debug endpoint to see all data in database"""
    try:
        from database import ExtractedData
        all_records = db.query(ExtractedData).all()

        results = []
        for record in all_records:
            record_dict = {
                "id": record.id,
                "type": record.type,
                "status": record.status,
                "source_file": record.source_file,
                "client_name": record.client_name,
                "email": record.email,
                "phone": record.phone,
                "company": record.company,
                "service_interest": record.service_interest,
                "amount": record.amount,
                "vat": record.vat,
                "total_amount": record.total_amount,
                "invoice_number": record.invoice_number,
                "priority": record.priority,
                "message": record.message,
                "email_type": record.email_type,
                "date_extracted": record.date_extracted.isoformat() if record.date_extracted else None
            }
            results.append(record_dict)

        # CORRECTED JSON RESPONSE with ensure_ascii=False
        json_content = json.dumps({
            "success": True,
            "total_records": len(results),
            "data": results
        }, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/statistics")
async def get_statistics(db: Session = Depends(get_db)):
    """Get system statistics"""
    try:
        from database import ExtractedData

        all_records = db.query(ExtractedData).all()

        stats = {
            "total_records": len(all_records),
            "pending": len([r for r in all_records if r.status == "pending"]),
            "approved": len([r for r in all_records if r.status == "approved"]),
            "rejected": len([r for r in all_records if r.status == "rejected"]),
            "forms": len([r for r in all_records if r.type == "FORM"]),
            "emails": len([r for r in all_records if r.type == "EMAIL"]),
            "invoices": len([r for r in all_records if r.type == "INVOICE"]),
            "total_amount": sum(r.total_amount for r in all_records if r.total_amount and r.status == "approved"),
            "last_update": datetime.now().isoformat()
        }

        # CORRECTED JSON RESPONSE with ensure_ascii=False
        json_content = json.dumps({"success": True, "data": stats}, ensure_ascii=False)
        return Response(content=json_content, media_type="application/json")

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn

    # Ensure required directories exist
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("exports", exist_ok=True)
    os.makedirs("static", exist_ok=True)

    uvicorn.run("main:app", host="localhost", port=8000, reload=True)
