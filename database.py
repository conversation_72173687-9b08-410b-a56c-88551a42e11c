import hashlib
import os
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import json

DATABASE_URL = "sqlite:///./automation_data.db"

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class ExtractedData(Base):
    __tablename__ = "extracted_data"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(String, nullable=False)  # FORM, EMAIL, INVOICE
    source_file = Column(String, nullable=False)
    file_hash = Column(String, nullable=True, index=True)  # For duplicate detection
    status = Column(String, default="pending")  # pending, approved, rejected
    date_extracted = Column(DateTime, default=datetime.utcnow)
    date_processed = Column(DateTime, nullable=True)

    # Client Data
    client_name = Column(String, nullable=True)
    email = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    company = Column(String, nullable=True)
    service_interest = Column(String, nullable=True)

    # Financial Data
    amount = Column(Float, nullable=True)
    vat = Column(Float, nullable=True)
    total_amount = Column(Float, nullable=True)
    invoice_number = Column(String, nullable=True)

    # Additional Data
    priority = Column(String, nullable=True)
    message = Column(Text, nullable=True)
    raw_data = Column(Text, nullable=True)  # JSON string of all extracted data

    # Email specific
    email_type = Column(String, nullable=True)  # client_inquiry, invoice_notification
    email_subject = Column(String, nullable=True)
    email_date = Column(String, nullable=True)

def create_tables():
    Base.metadata.create_all(bind=engine)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def calculate_file_hash(file_path: str) -> str:
    """Calculate SHA-256 hash of file content"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {file_path}: {str(e)}")
        return None

def check_duplicate_file(db, file_path: str) -> dict:
    """Check if file already exists in database by filename or hash"""
    filename = os.path.basename(file_path)
    file_hash = calculate_file_hash(file_path)

    # Check by filename first
    existing_by_name = db.query(ExtractedData).filter(ExtractedData.source_file == filename).first()

    # Check by hash if we have one
    existing_by_hash = None
    if file_hash:
        existing_by_hash = db.query(ExtractedData).filter(ExtractedData.file_hash == file_hash).first()

    if existing_by_name or existing_by_hash:
        existing_record = existing_by_name or existing_by_hash
        return {
            "is_duplicate": True,
            "duplicate_type": "filename" if existing_by_name else "content",
            "existing_record": {
                "id": existing_record.id,
                "filename": existing_record.source_file,
                "status": existing_record.status,
                "date_extracted": existing_record.date_extracted.isoformat() if existing_record.date_extracted else None,
                "client_name": existing_record.client_name,
                "company": existing_record.company
            }
        }

    return {"is_duplicate": False, "file_hash": file_hash}

def save_extracted_data(db, data_dict):
    """Save extracted data to database"""
    try:
        # Clean data_dict to remove None values and ensure proper types
        clean_data = {}
        for key, value in data_dict.items():
            if hasattr(ExtractedData, key):
                if value is not None and value != '':
                    clean_data[key] = value

        # Ensure we have required fields
        if 'type' not in clean_data:
            clean_data['type'] = 'UNKNOWN'
        if 'source_file' not in clean_data:
            clean_data['source_file'] = 'unknown'

        print(f"Creating ExtractedData with: {clean_data}")  # Debug log

        db_item = ExtractedData(**clean_data)
        db.add(db_item)
        db.commit()
        db.refresh(db_item)

        print(f"Successfully saved record with ID: {db_item.id}")  # Debug log
        return db_item

    except Exception as e:
        print(f"Database save error: {str(e)}")
        print(f"Data that failed: {data_dict}")
        db.rollback()
        raise e

def get_all_pending_data(db):
    """Get all pending records for approval"""
    return db.query(ExtractedData).filter(ExtractedData.status == "pending").all()

def get_all_data_for_debug(db):
    """Get all records for debugging"""
    return db.query(ExtractedData).all()

def update_record_status(db, record_id, status, updated_data=None):
    """Update record status and optionally the data"""
    record = db.query(ExtractedData).filter(ExtractedData.id == record_id).first()
    if record:
        record.status = status
        record.date_processed = datetime.utcnow()

        if updated_data:
            for key, value in updated_data.items():
                if hasattr(record, key):
                    setattr(record, key, value)

        db.commit()
        db.refresh(record)
        return record
    return None

def get_approved_data(db):
    """Get all approved records for export"""
    return db.query(ExtractedData).filter(ExtractedData.status == "approved").all()

def delete_record(db, record_id):
    """Delete a record. Returns True if successful, False if record not found."""
    try:
        # Convert to int if it's a string
        if isinstance(record_id, str):
            record_id = int(record_id)
            
        record = db.query(ExtractedData).filter(ExtractedData.id == record_id).first()
        if record:
            db.delete(record)
            db.commit()
            print(f"DATABASE: Successfully deleted record ID {record_id}")
            return True
        else:
            print(f"DATABASE: Record ID {record_id} not found")
            return False
    except Exception as e:
        print(f"DATABASE ERROR: Failed to delete record {record_id}: {str(e)}")
        db.rollback()
        raise e


def delete_records(db, record_ids):
    """Delete multiple records by IDs. Returns number of deleted records."""
    if not record_ids:
        return 0
        
    # Ensure IDs are unique integers
    try:
        ids = list({int(rid) for rid in record_ids if isinstance(rid, (int, str)) and str(rid).isdigit()})
    except (ValueError, TypeError):
        print(f"DATABASE ERROR: Invalid record IDs: {record_ids}")
        return 0
        
    if not ids:
        return 0
        
    print(f"DATABASE: Attempting to delete records with IDs: {ids}")
    deleted_count = 0
    
    try:
        for rid in ids:
            record = db.query(ExtractedData).filter(ExtractedData.id == rid).first()
            if record:
                db.delete(record)
                deleted_count += 1
                print(f"DATABASE: Marked record {rid} for deletion")
            else:
                print(f"DATABASE: Record {rid} not found")
                
        if deleted_count > 0:
            db.commit()
            print(f"DATABASE: Successfully committed deletion of {deleted_count} records")
        
        return deleted_count
        
    except Exception as e:
        print(f"DATABASE ERROR: Failed to delete records: {str(e)}")
        db.rollback()
        raise e


def delete_all_pending(db):
    """Delete all records with status 'pending'. Returns number of deleted records."""
    try:
        pending_records = db.query(ExtractedData).filter(ExtractedData.status == "pending").all()
        deleted_count = len(pending_records)
        
        print(f"DATABASE: Found {deleted_count} pending records to delete")
        
        for record in pending_records:
            db.delete(record)
            
        if deleted_count > 0:
            db.commit()
            print(f"DATABASE: Successfully deleted {deleted_count} pending records")
            
        return deleted_count
        
    except Exception as e:
        print(f"DATABASE ERROR: Failed to delete all pending records: {str(e)}")
        db.rollback()
        raise e